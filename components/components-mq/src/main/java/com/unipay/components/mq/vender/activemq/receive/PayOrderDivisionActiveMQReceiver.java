
package com.unipay.components.mq.vender.activemq.receive;

import com.unipay.components.mq.constant.MQVenderCS;
import com.unipay.components.mq.model.PayOrderDivisionMQ;
import com.unipay.components.mq.vender.IMQMsgReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

/**
 * activeMQ 消息接收器：仅在vender=activeMQ时 && 项目实现IMQReceiver接口时 进行实例化
 * 业务：  支付订单分账通知
 *
 * <AUTHOR> 
 * @date 2021/8/22 16:43
 */
@Component
@ConditionalOnProperty(name = MQVenderCS.YML_VENDER_KEY, havingValue = MQVenderCS.ACTIVE_MQ)
@ConditionalOnBean(PayOrderDivisionMQ.IMQReceiver.class)
public class PayOrderDivisionActiveMQReceiver implements IMQMsgReceiver {

    @Autowired
    private PayOrderDivisionMQ.IMQReceiver mqReceiver;

    /** 接收 【 queue 】 类型的消息 **/
    @Override
    @JmsListener(destination = PayOrderDivisionMQ.MQ_NAME)
    public void receiveMsg(String msg){
        mqReceiver.receive(PayOrderDivisionMQ.parse(msg));
    }

}
