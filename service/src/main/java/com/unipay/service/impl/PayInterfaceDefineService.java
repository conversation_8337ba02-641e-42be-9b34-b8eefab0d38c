
package com.unipay.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.entity.PayInterfaceDefine;
import com.unipay.service.mapper.PayInterfaceDefineMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 支付接口定义表 服务实现类
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2021-04-27
 */
@Service
public class PayInterfaceDefineService extends ServiceImpl<PayInterfaceDefineMapper, PayInterfaceDefine> {

}
