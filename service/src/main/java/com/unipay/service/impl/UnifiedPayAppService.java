package com.unipay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.constants.ApiCodeEnum;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.UnifiedPayApp;
import com.unipay.core.exception.BizException;
import com.unipay.core.utils.StringKit;
import com.unipay.service.mapper.UnifiedPayAppMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 统一支付应用表 服务实现类
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2025-09-25
 */
@Service
public class UnifiedPayAppService extends ServiceImpl<UnifiedPayAppMapper, UnifiedPayApp> {

    /**
     * 获取默认的统一支付应用
     * @return 默认应用
     */
    public UnifiedPayApp getDefaultApp() {
        return getOne(UnifiedPayApp.gw()
                .eq(UnifiedPayApp::getIsDefault, CS.YES)
                .eq(UnifiedPayApp::getState, CS.YES)
                .last("LIMIT 1"));
    }

    /**
     * 设置默认应用
     * @param appId 应用ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultApp(String appId) {
        // 先取消所有默认应用
        update(new UnifiedPayApp().setIsDefault(CS.NO),
                UnifiedPayApp.gw().eq(UnifiedPayApp::getIsDefault, CS.YES));
        
        // 设置新的默认应用
        UnifiedPayApp app = getById(appId);
        if (app == null) {
            throw new BizException("应用不存在");
        }
        
        app.setIsDefault(CS.YES);
        updateById(app);
    }

    /**
     * 分页查询统一支付应用
     * @param iPage 分页参数
     * @param unifiedPayApp 查询条件
     * @return 分页结果
     */
    public IPage<UnifiedPayApp> selectPage(IPage iPage, UnifiedPayApp unifiedPayApp) {
        LambdaQueryWrapper<UnifiedPayApp> wrapper = UnifiedPayApp.gw();
        
        if (StringUtils.isNotEmpty(unifiedPayApp.getAppId())) {
            wrapper.eq(UnifiedPayApp::getAppId, unifiedPayApp.getAppId());
        }
        if (StringUtils.isNotEmpty(unifiedPayApp.getAppName())) {
            wrapper.like(UnifiedPayApp::getAppName, unifiedPayApp.getAppName());
        }
        if (unifiedPayApp.getState() != null) {
            wrapper.eq(UnifiedPayApp::getState, unifiedPayApp.getState());
        }
        if (unifiedPayApp.getIsDefault() != null) {
            wrapper.eq(UnifiedPayApp::getIsDefault, unifiedPayApp.getIsDefault());
        }
        
        wrapper.orderByDesc(UnifiedPayApp::getCreatedAt);

        IPage<UnifiedPayApp> pages = this.page(iPage, wrapper);

        // 对应用私钥进行脱敏处理
        pages.getRecords().forEach(item -> 
            item.setAppSecret(StringKit.str2Star(item.getAppSecret(), 6, 6, 6)));

        return pages;
    }

    /**
     * 根据ID查询应用详情（脱敏处理）
     * @param appId 应用ID
     * @return 应用详情
     */
    public UnifiedPayApp selectById(String appId) {
        UnifiedPayApp app = this.getById(appId);
        if (app == null) {
            return null;
        }
        app.setAppSecret(StringKit.str2Star(app.getAppSecret(), 6, 6, 6));
        return app;
    }

    /**
     * 删除统一支付应用
     * @param appId 应用ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeByAppId(String appId) {
        UnifiedPayApp app = getById(appId);
        if (app == null) {
            throw new BizException("应用不存在");
        }
        
        // 如果是默认应用，不允许删除
        if (CS.YES == app.getIsDefault()) {
            throw new BizException("默认应用不允许删除");
        }
        
        // TODO: 检查是否有商户绑定了该应用，如果有则不允许删除
        
        if (!removeById(appId)) {
            throw new BizException(ApiCodeEnum.SYS_OPERATION_FAIL_DELETE);
        }
    }
}
