server:
  port: 9219 #设置端口

spring: 
  data:
    redis:
      database: 3
# knife4j APIDOC文档
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.unipay.agent.ctrl
knife4j:
  enable: false  # 生产环境建议设置为 false，避免API信息泄露风险

#系统业务参数
isys:
  jwt-secret: t7w3P8X6472jooyiPhohhwisPWQPS#qWc3u@ #生成jwt的秘钥。 要求每个系统有单独的秘钥管理机制。
  #是否允许跨域请求 [生产环境建议关闭， 若api与前端项目没有在同一个域名下时，应开启此配置或在nginx统一配置允许跨域]
  allow-cors: true
  #是否内存缓存配置信息: true表示开启如支付网关地址/商户应用配置/服务商配置等， 开启后需检查MQ的广播模式是否正常； false表示直接查询DB.
  cache-config: false
  mq:
    vender: activeMQ  #  切换MQ厂商， 支持：【 activeMQ  rabbitMQ  rocketMQ  aliYunRocketMQ 】， 需正确配置 【对应的yml参数】 和 【components-mq项目下pom.xml中的依赖包】。



