
package com.unipay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.entity.AgentMchRelation;
import com.unipay.service.impl.AgentInfoService;
import com.unipay.service.mapper.AgentMchRelationMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 代理商商户关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AgentMchRelationService extends ServiceImpl<AgentMchRelationMapper, AgentMchRelation> {

    @Autowired
    private AgentInfoService agentInfoService;

    /**
     * 根据代理商号查询关联的商户号列表（包括下级代理商的商户）
     * @param agentNo 代理商号
     * @return 商户号列表
     */
    public List<String> getMchNosByAgentNo(String agentNo) {
        // 获取当前代理商及其所有下级代理商号列表（不包括上级代理商）
        List<String> allSubAgentNos = agentInfoService.getAllSubAgentNos(agentNo);

        if (allSubAgentNos.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询所有这些代理商关联的商户号
        return baseMapper.selectMchNosByAgentNos(allSubAgentNos);
    }

    /**
     * 根据商户号查询关联的代理商号列表
     * @param mchNo 商户号
     * @return 代理商号列表
     */
    public List<String> getAgentNosByMchNo(String mchNo) {
        return baseMapper.selectAgentNosByMchNo(mchNo);
    }

    /**
     * 分页查询代理商商户关系
     * @param page 分页参数
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param relationType 关系类型
     * @return 关系分页数据
     */
    public IPage<AgentMchRelation> selectPage(IPage<AgentMchRelation> page, String agentNo, String mchNo, Byte relationType) {
        LambdaQueryWrapper<AgentMchRelation> wrapper = AgentMchRelation.gw();
        
        if (StringUtils.isNotBlank(agentNo)) {
            wrapper.eq(AgentMchRelation::getAgentNo, agentNo);
        }
        if (StringUtils.isNotBlank(mchNo)) {
            wrapper.like(AgentMchRelation::getMchNo, mchNo);
        }
        if (relationType != null) {
            wrapper.eq(AgentMchRelation::getRelationType, relationType);
        }
        
        wrapper.orderByDesc(AgentMchRelation::getCreatedAt);
        return page(page, wrapper);
    }

    /**
     * 创建代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param relationType 关系类型
     * @param profitRate 分润比例
     * @param createdUid 创建者用户ID
     * @param createdBy 创建者姓名
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRelation(String agentNo, String mchNo, Byte relationType,
                                 java.math.BigDecimal profitRate, Long createdUid, String createdBy) {
        // 检查关系是否已存在
        AgentMchRelation existRelation = getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));

        if (existRelation != null) {
            return false; // 关系已存在
        }

        AgentMchRelation relation = new AgentMchRelation();
        relation.setAgentNo(agentNo);
        relation.setMchNo(mchNo);
        relation.setRelationType(relationType);
        relation.setProfitRate(profitRate);
        relation.setState(AgentMchRelation.STATE_NORMAL);
        relation.setCreatedUid(createdUid);
        relation.setCreatedBy(createdBy);

        return save(relation);
    }

    /**
     * 创建代理商商户层级关系（包括为上级代理商创建间接关系）
     * @param agentNo 直接代理商号
     * @param mchNo 商户号
     * @param profitRate 分润比例
     * @param createdUid 创建者用户ID
     * @param createdBy 创建者姓名
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createHierarchicalRelations(String agentNo, String mchNo,
                                              java.math.BigDecimal profitRate, Long createdUid, String createdBy) {
        // 1. 创建直接关系
        boolean directResult = createRelation(agentNo, mchNo, AgentMchRelation.RELATION_TYPE_DIRECT,
                                            profitRate, createdUid, createdBy);

        if (!directResult) {
            return false;
        }

        // 2. 获取当前代理商信息
        com.unipay.core.entity.AgentInfo currentAgent = agentInfoService.getByAgentNo(agentNo);
        if (currentAgent == null || StringUtils.isBlank(currentAgent.getParentAgentNo())) {
            return true; // 没有上级代理商，只创建直接关系即可
        }

        // 3. 为所有上级代理商创建间接关系
        String parentAgentNo = currentAgent.getParentAgentNo();
        while (StringUtils.isNotBlank(parentAgentNo)) {
            // 检查间接关系是否已存在
            AgentMchRelation existIndirectRelation = getOne(AgentMchRelation.gw()
                    .eq(AgentMchRelation::getAgentNo, parentAgentNo)
                    .eq(AgentMchRelation::getMchNo, mchNo));

            if (existIndirectRelation == null) {
                // 创建间接关系
                AgentMchRelation indirectRelation = new AgentMchRelation();
                indirectRelation.setAgentNo(parentAgentNo);
                indirectRelation.setMchNo(mchNo);
                indirectRelation.setRelationType(AgentMchRelation.RELATION_TYPE_INDIRECT);
                indirectRelation.setProfitRate(new java.math.BigDecimal("0.00")); // 间接关系默认分润为0
                indirectRelation.setState(AgentMchRelation.STATE_NORMAL);
                indirectRelation.setCreatedUid(createdUid);
                indirectRelation.setCreatedBy(createdBy);

                save(indirectRelation);
            }

            // 获取上级代理商的上级代理商
            com.unipay.core.entity.AgentInfo parentAgent = agentInfoService.getByAgentNo(parentAgentNo);
            parentAgentNo = (parentAgent != null) ? parentAgent.getParentAgentNo() : null;
        }

        return true;
    }

    /**
     * 修复现有商户的层级关系数据
     * 为现有的直接关系商户，补充创建上级代理商的间接关系
     */
    @Transactional(rollbackFor = Exception.class)
    public void fixExistingHierarchicalRelations() {
        // 查询所有直接关系的记录
        List<AgentMchRelation> directRelations = list(AgentMchRelation.gw()
                .eq(AgentMchRelation::getRelationType, AgentMchRelation.RELATION_TYPE_DIRECT)
                .eq(AgentMchRelation::getState, AgentMchRelation.STATE_NORMAL));

        for (AgentMchRelation directRelation : directRelations) {
            String agentNo = directRelation.getAgentNo();
            String mchNo = directRelation.getMchNo();

            // 获取当前代理商信息
            com.unipay.core.entity.AgentInfo currentAgent = agentInfoService.getByAgentNo(agentNo);
            if (currentAgent == null || StringUtils.isBlank(currentAgent.getParentAgentNo())) {
                continue; // 没有上级代理商，跳过
            }

            // 为所有上级代理商创建间接关系
            String parentAgentNo = currentAgent.getParentAgentNo();
            while (StringUtils.isNotBlank(parentAgentNo)) {
                // 检查间接关系是否已存在
                AgentMchRelation existIndirectRelation = getOne(AgentMchRelation.gw()
                        .eq(AgentMchRelation::getAgentNo, parentAgentNo)
                        .eq(AgentMchRelation::getMchNo, mchNo));

                if (existIndirectRelation == null) {
                    // 创建间接关系
                    AgentMchRelation indirectRelation = new AgentMchRelation();
                    indirectRelation.setAgentNo(parentAgentNo);
                    indirectRelation.setMchNo(mchNo);
                    indirectRelation.setRelationType(AgentMchRelation.RELATION_TYPE_INDIRECT);
                    indirectRelation.setProfitRate(new java.math.BigDecimal("0.00"));
                    indirectRelation.setState(AgentMchRelation.STATE_NORMAL);
                    indirectRelation.setCreatedUid(directRelation.getCreatedUid());
                    indirectRelation.setCreatedBy("系统修复");

                    save(indirectRelation);
                }

                // 获取上级代理商的上级代理商
                com.unipay.core.entity.AgentInfo parentAgent = agentInfoService.getByAgentNo(parentAgentNo);
                parentAgentNo = (parentAgent != null) ? parentAgent.getParentAgentNo() : null;
            }
        }
    }

    /**
     * 更新分润比例
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @param profitRate 新的分润比例
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProfitRate(String agentNo, String mchNo, java.math.BigDecimal profitRate) {
        AgentMchRelation relation = getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
        
        if (relation == null) {
            return false;
        }
        
        relation.setProfitRate(profitRate);
        return updateById(relation);
    }

    /**
     * 删除代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRelation(String agentNo, String mchNo) {
        return remove(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
    }

    /**
     * 批量创建代理商商户关系
     * @param relations 关系列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateRelations(List<AgentMchRelation> relations) {
        if (relations == null || relations.isEmpty()) {
            return true;
        }
        
        return baseMapper.batchInsert(relations) > 0;
    }

    /**
     * 根据代理商号删除所有关系
     * @param agentNo 代理商号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAgentNo(String agentNo) {
        return remove(AgentMchRelation.gw().eq(AgentMchRelation::getAgentNo, agentNo));
    }

    /**
     * 根据商户号删除所有关系
     * @param mchNo 商户号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMchNo(String mchNo) {
        return remove(AgentMchRelation.gw().eq(AgentMchRelation::getMchNo, mchNo));
    }

    /**
     * 检查代理商是否有关联商户
     * @param agentNo 代理商号
     * @return 是否有关联商户
     */
    public boolean hasRelatedMerchants(String agentNo) {
        return count(AgentMchRelation.gw().eq(AgentMchRelation::getAgentNo, agentNo)) > 0;
    }

    /**
     * 获取代理商商户关系
     * @param agentNo 代理商号
     * @param mchNo 商户号
     * @return 关系信息
     */
    public AgentMchRelation getRelation(String agentNo, String mchNo) {
        return getOne(AgentMchRelation.gw()
                .eq(AgentMchRelation::getAgentNo, agentNo)
                .eq(AgentMchRelation::getMchNo, mchNo));
    }

}
