#################################
# spring boot支持外部application.yml  读取优先级为：
#   1、file:./config/（当前目录下的config文件夹）
#   2、file:./（当前目录）
#   3、classpath:/config/（classpath下的config目录）
#   4、classpath:/（classpath根目录）
#   建议： 如果是jar则放置到与jar相同的目录下，  如果解压文件放置到classpath: config目录下。 (需要将文件重命名为 application.yml )
#
#   该yml文件只配置与环境相关的参数， 其他配置读取项目下的配置项
#
#################################
server:
  port: 9217 #设置端口
  servlet:
    context-path: / #设置应用的目录.  前缀需要带/, 无需设置后缀, 示例 【 /xxx 】 or 【 / 】
  web:
    resources:
      static-locations: ./unipay-web-ui/unipay-ui-manager/dist #项目静态资源路径 （可直接通过http访问）
spring: 
  data:
    redis:
      database: 1
# knife4j APIDOC文档
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.unipay.mgr.ctrl
knife4j:
  enable: false

#系统业务参数
isys:
  jwt-secret: a7$kL9#8z!PQ1nY4T@f3Uv6XbZ0mQw #生成jwt的秘钥。 要求每个系统有单独的秘钥管理机制。
  #是否允许跨域请求 [生产环境建议关闭， 若api与前端项目没有在同一个域名下时，应开启此配置或在nginx统一配置允许跨域]
  allow-cors: true
  #是否内存缓存配置信息: true表示开启如支付网关地址/商户应用配置/服务商配置等， 开启后需检查MQ的广播模式是否正常； false表示直接查询DB.
  cache-config: true
  mq:
    vender: activeMQ  #  切换MQ厂商， 支持：【 activeMQ  rabbitMQ  rocketMQ  aliYunRocketMQ 】， 需正确配置 【对应的yml参数】 和 【jeepay-components-mq项目下pom.xml中的依赖包】。
