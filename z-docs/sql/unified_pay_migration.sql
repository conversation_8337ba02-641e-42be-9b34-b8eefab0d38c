-- Active: 1758872376809@@127.0.0.1@3306@unipay-db
-- 统一支付应用重构数据库迁移脚本
-- 执行日期: 2025-09-25
-- 说明: 重构应用创建方式，从每个商户自动创建应用改为使用统一应用

-- 1. 创建统一支付应用表
DROP TABLE IF EXISTS t_unified_pay_app;
CREATE TABLE `t_unified_pay_app` (
    `app_id` VARCHAR(64) NOT NULL COMMENT '应用ID',
    `app_name` VARCHAR(64) NOT NULL COMMENT '应用名称',
    `state` TINYINT(6) NOT NULL DEFAULT 1 COMMENT '应用状态: 0-停用, 1-正常',
    `app_secret` VARCHAR(128) NOT NULL COMMENT '应用私钥',
    `is_default` TINYINT(6) NOT NULL DEFAULT 0 COMMENT '是否为默认应用: 0-否, 1-是',
    `remark` VARCHAR(128) DEFAULT NULL COMMENT '备注',
    `created_uid` BIGINT(20) DEFAULT NULL COMMENT '创建者用户ID',
    `created_by` VARCHAR(64) DEFAULT NULL COMMENT '创建者姓名',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`app_id`),
    KEY `idx_state` (`state`),
    KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一支付应用表';

-- 2. 修改商户信息表，添加统一应用ID和手续费率字段
ALTER TABLE `t_mch_info` 
ADD COLUMN `unified_app_id` VARCHAR(64) DEFAULT NULL COMMENT '绑定的统一应用ID' AFTER `init_user_id`,
ADD COLUMN `fee_rate` DECIMAL(20,6) DEFAULT 0.006000 COMMENT '手续费收取率（百分比，如0.6表示0.6%）' AFTER `unified_app_id`;

-- 添加索引
ALTER TABLE `t_mch_info` ADD KEY `idx_unified_app_id` (`unified_app_id`);

-- 3. 创建商户收益记录表
DROP TABLE IF EXISTS t_mch_income_record;
CREATE TABLE `t_mch_income_record` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `mch_no` VARCHAR(64) NOT NULL COMMENT '商户号',
    `pay_order_id` VARCHAR(30) NOT NULL COMMENT '支付订单号',
    `order_amount` BIGINT(20) NOT NULL COMMENT '订单金额（单位：分）',
    `fee_amount` BIGINT(20) NOT NULL COMMENT '手续费金额（单位：分）',
    `fee_rate` DECIMAL(20,6) NOT NULL COMMENT '手续费率（百分比）',
    `income_amount` BIGINT(20) NOT NULL COMMENT '商户实际收益（单位：分）',
    `income_date` DATE NOT NULL COMMENT '收益日期',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_pay_order_id` (`pay_order_id`),
    KEY `idx_mch_no` (`mch_no`),
    KEY `idx_income_date` (`income_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户收益记录表';

-- 4. 插入默认的统一支付应用
INSERT INTO `t_unified_pay_app` (
    `app_id`, 
    `app_name`, 
    `state`, 
    `app_secret`, 
    `is_default`, 
    `remark`, 
    `created_by`
) VALUES (
    'DEFAULT_UNIFIED_APP_001', 
    '默认统一支付应用', 
    1, 
    'DEFAULT_SECRET_KEY_FOR_UNIFIED_PAY_APP_PLEASE_CHANGE_IN_PRODUCTION_ENVIRONMENT_FOR_SECURITY_REASONS_123456789', 
    1, 
    '系统默认的统一支付应用，所有商户共用此应用进行交易', 
    'SYSTEM'
);

-- 5. 为现有商户绑定默认的统一应用
UPDATE `t_mch_info` 
SET `unified_app_id` = 'DEFAULT_UNIFIED_APP_001',
    `fee_rate` = 0.006000
WHERE `unified_app_id` IS NULL;

-- 6. 创建商户收益记录的统计视图（可选）
CREATE OR REPLACE VIEW `v_mch_income_stats` AS
SELECT 
    `mch_no`,
    COUNT(*) as `total_orders`,
    SUM(`order_amount`) as `total_order_amount`,
    SUM(`fee_amount`) as `total_fee_amount`,
    SUM(`income_amount`) as `total_income_amount`,
    AVG(`fee_rate`) as `avg_fee_rate`,
    DATE(`income_date`) as `income_date`
FROM `t_mch_income_record`
GROUP BY `mch_no`, DATE(`income_date`);

-- 7. 添加权限配置（需要手动执行或通过权限管理界面添加）
-- 统一支付应用管理权限
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP', '统一支付应用管理', 'file-done', '/unifiedApps', 'UnifiedAppPage', 'ML', 0, 1, 'ENT_PC', '15', 'MGR', now(), now());
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP_LIST', '统一支付应用列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UNIFIED_PAY_APP', '0', 'MGR', now(), now());
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP_VIEW', '统一支付应用详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UNIFIED_PAY_APP', '10', 'MGR', now(), now());
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP_ADD', '新建统一支付应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UNIFIED_PAY_APP', '20', 'MGR', now(), now());
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP_EDIT', '修改统一支付应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UNIFIED_PAY_APP', '30', 'MGR', now(), now());
insert into t_sys_entitlement values('ENT_UNIFIED_PAY_APP_DEL', '删除统一支付应用', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UNIFIED_PAY_APP', '40', 'MGR', now(), now());

-- 角色权限关联
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_LIST') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_VIEW') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_ADD') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_EDIT') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_DEL') ON DUPLICATE KEY UPDATE role_id=VALUES(role_id), ent_id=VALUES(ent_id);


-- 8. 备份说明
-- 在执行此脚本前，建议备份以下表：
-- - t_mch_info
-- - t_mch_app (如果需要保留原有应用数据)
-- 
-- 备份命令示例：
-- mysqldump -u username -p database_name t_mch_info t_mch_app > backup_before_unified_pay_migration.sql

-- 9. 回滚脚本（如需回滚，请谨慎执行）

-- 回滚步骤：
-- 1. 删除新增的字段
ALTER TABLE `t_mch_info` DROP COLUMN `unified_app_id`;
ALTER TABLE `t_mch_info` DROP COLUMN `fee_rate`;

-- 2. 删除新增的表
DROP TABLE IF EXISTS `t_unified_pay_app`;
DROP TABLE IF EXISTS `t_mch_income_record`;
DROP VIEW IF EXISTS `v_mch_income_stats`;

-- 3. 恢复原有的商户应用创建逻辑（需要代码层面的修改）
