
package com.unipay.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unipay.core.model.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 代理商分润记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Schema(description = "代理商分润记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_agent_profit_record")
public class AgentProfitRecord extends BaseModel implements Serializable {

    public static final LambdaQueryWrapper<AgentProfitRecord> gw(){
        return new LambdaQueryWrapper<>();
    }

    private static final long serialVersionUID = 1L;

    // 分润状态
    public static final byte STATE_WAIT_SETTLE = 0; //待结算
    public static final byte STATE_SETTLED = 1; //已结算
    public static final byte STATE_CANCELLED = 2; //已取消

    /**
     * ID
     */
    @Schema(title = "id", description = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 代理商号
     */
    @Schema(title = "agentNo", description = "代理商号")
    private String agentNo;

    /**
     * 商户号
     */
    @Schema(title = "mchNo", description = "商户号")
    private String mchNo;

    /**
     * 支付订单号
     */
    @Schema(title = "payOrderId", description = "支付订单号")
    private String payOrderId;

    /**
     * 订单金额,单位分
     */
    @Schema(title = "orderAmount", description = "订单金额,单位分")
    private Long orderAmount;

    /**
     * 商户手续费,单位分
     */
    @Schema(title = "mchFeeAmount", description = "商户手续费,单位分")
    private Long mchFeeAmount;

    /**
     * 分润比例
     */
    @Schema(title = "profitRate", description = "分润比例")
    private BigDecimal profitRate;

    /**
     * 分润金额,单位分
     */
    @Schema(title = "profitAmount", description = "分润金额,单位分")
    private Long profitAmount;

    /**
     * 分润日期
     */
    @Schema(title = "profitDate", description = "分润日期")
    private Date profitDate;

    /**
     * 分润状态: 0-待结算, 1-已结算, 2-已取消
     */
    @Schema(title = "state", description = "分润状态: 0-待结算, 1-已结算, 2-已取消")
    private Byte state;

    /**
     * 结算时间
     */
    @Schema(title = "settleTime", description = "结算时间")
    private Date settleTime;

    /**
     * 备注
     */
    @Schema(title = "remark", description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(title = "createdAt", description = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Schema(title = "updatedAt", description = "更新时间")
    private Date updatedAt;

}
