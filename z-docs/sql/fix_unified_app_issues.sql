-- Active: 1758872376809@@127.0.0.1@3306@unipay-db
-- 修复统一应用重构后的问题
-- 执行日期: 2025-09-26
-- 说明: 修复商户创建、菜单显示、支付流程等问题

-- 1. 修复权限配置中的菜单路径和组件名称
UPDATE t_sys_entitlement 
SET menu_uri = '/unifiedApps', component_name = 'UnifiedAppPage'
WHERE ent_id = 'ENT_UNIFIED_PAY_APP';

-- 2. 检查并修复统一应用表数据
-- 确保有默认应用存在
INSERT IGNORE INTO `t_unified_pay_app` (
    `app_id`, 
    `app_name`, 
    `state`, 
    `app_secret`, 
    `is_default`, 
    `remark`, 
    `created_by`
) VALUES (
    'DEFAULT_UNIFIED_APP_001', 
    '默认统一支付应用', 
    1, 
    'DEFAULT_SECRET_KEY_FOR_UNIFIED_PAY_APP_PLEASE_CHANGE_IN_PRODUCTION_ENVIRONMENT_FOR_SECURITY_REASONS_123456789', 
    1, 
    '系统默认的统一支付应用，所有商户共用此应用进行交易', 
    'SYSTEM'
);

-- 3. 确保所有商户都绑定了统一应用
UPDATE `t_mch_info` 
SET `unified_app_id` = 'DEFAULT_UNIFIED_APP_001',
    `fee_rate` = 0.006000
WHERE `unified_app_id` IS NULL OR `unified_app_id` = '';

-- 4. 检查权限是否正确分配给管理员角色
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_LIST');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_VIEW');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_ADD');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_EDIT');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_UNIFIED_PAY_APP_DEL');

-- 5. 添加商户收益管理权限（如果还没有的话）
INSERT IGNORE INTO t_sys_entitlement VALUES('ENT_MCH_INCOME', '商户收益管理', 'dollar', '/mchIncome', 'MchIncomePage', 'ML', 0, 1, 'ENT_PC', '16', 'MGR', now(), now());
INSERT IGNORE INTO t_sys_entitlement VALUES('ENT_MCH_INCOME_LIST', '商户收益列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INCOME', '0', 'MGR', now(), now());
INSERT IGNORE INTO t_sys_entitlement VALUES('ENT_MCH_INCOME_VIEW', '商户收益详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INCOME', '10', 'MGR', now(), now());
INSERT IGNORE INTO t_sys_entitlement VALUES('ENT_MCH_INCOME_STATS', '商户收益统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INCOME', '20', 'MGR', now(), now());

-- 为管理员角色分配商户收益管理权限
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_MCH_INCOME');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_MCH_INCOME_LIST');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_MCH_INCOME_VIEW');
INSERT IGNORE INTO t_sys_role_ent_rela (role_id, ent_id) VALUES ('ROLE_ADMIN', 'ENT_MCH_INCOME_STATS');

-- 6. 验证查询
-- 检查统一应用
SELECT '=== 统一应用检查 ===' as info;
SELECT app_id, app_name, state, is_default FROM t_unified_pay_app;

-- 检查商户绑定情况
SELECT '=== 商户绑定检查 ===' as info;
SELECT mch_no, mch_name, unified_app_id, fee_rate FROM t_mch_info LIMIT 5;

-- 检查权限配置
SELECT '=== 权限配置检查 ===' as info;
SELECT ent_id, ent_name, menu_uri, component_name FROM t_sys_entitlement WHERE ent_id LIKE 'ENT_UNIFIED_PAY_APP%';

-- 检查角色权限关联
SELECT '=== 角色权限关联检查 ===' as info;
SELECT r.role_id, r.role_name, e.ent_id, e.ent_name 
FROM t_sys_role_ent_rela rel
JOIN t_sys_role r ON rel.role_id = r.role_id
JOIN t_sys_entitlement e ON rel.ent_id = e.ent_id
WHERE e.ent_id LIKE 'ENT_UNIFIED_PAY_APP%'
ORDER BY r.role_id, e.ent_id;
