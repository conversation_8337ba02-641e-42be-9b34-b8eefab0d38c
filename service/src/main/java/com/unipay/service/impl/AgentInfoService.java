package com.unipay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.AgentInfo;
import com.unipay.core.entity.SysUser;
import com.unipay.core.entity.SysUserAuth;
import com.unipay.core.entity.SysUserRoleRela;
import com.unipay.core.exception.BizException;
import com.unipay.core.utils.StringKit;
import com.unipay.core.utils.SeqKit;
import java.util.List;
import com.unipay.service.mapper.AgentInfoMapper;
import com.unipay.service.impl.SysUserService;
import com.unipay.service.impl.SysUserAuthService;
import com.unipay.service.impl.SysUserRoleRelaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 代理商信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AgentInfoService extends ServiceImpl<AgentInfoMapper, AgentInfo> {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserAuthService sysUserAuthService;

    @Autowired
    private SysUserRoleRelaService sysUserRoleRelaService;

    /**
     * 根据代理商号查询代理商信息
     */
    public AgentInfo getByAgentNo(String agentNo) {
        return getOne(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo));
    }

    /**
     * 创建代理商和系统用户
     * @param agentInfo 代理商信息
     * @param loginPassword 登录密码
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createAgentWithUser(AgentInfo agentInfo, String loginPassword) {
        try {
            // 自动生成代理商号
            if (StringUtils.isBlank(agentInfo.getAgentNo())) {
                agentInfo.setAgentNo(SeqKit.genAgentNo());
            }

            // 构建代理商路径
            String agentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
            agentInfo.setAgentPath(agentPath);
            
            // 设置代理商层级
            if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
                agentInfo.setAgentLevel((byte) 1);
            } else {
                AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
                if (parentAgent != null) {
                    agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
                }
            }
            
            // 设置默认状态
            if (agentInfo.getState() == null) {
                agentInfo.setState(AgentInfo.STATE_NORMAL);
            }
            
            // 保存代理商信息（先不保存loginUsername，避免数据库字段不存在的问题）
            String tempLoginUsername = agentInfo.getLoginUsername();
            agentInfo.setLoginUsername(null); // 临时设置为null，避免数据库字段不存在的问题

            boolean agentResult = save(agentInfo);
            if (!agentResult) {
                throw new RuntimeException("创建代理商失败");
            }

            // 创建系统用户
            if (StringUtils.isNotBlank(loginPassword)) {
                log.info("开始创建代理商系统用户，loginUsername: {}, agentNo: {}", tempLoginUsername, agentInfo.getAgentNo());

                if (StringUtils.isNotBlank(tempLoginUsername)) {
                    // 创建系统用户
                    SysUser sysUser = new SysUser();
                    sysUser.setLoginUsername(tempLoginUsername);
                    sysUser.setRealname(agentInfo.getContactName());
                    sysUser.setTelphone(agentInfo.getContactTel());
                    sysUser.setUserNo(StringKit.getUUID(8));
                    sysUser.setSex(CS.SEX_MALE);
                    sysUser.setState((byte) CS.PUB_USABLE);
                    sysUser.setIsAdmin(CS.YES); // 代理商管理员应该是管理员
                    sysUser.setBelongInfoId(agentInfo.getAgentNo());
                    sysUser.setCreatedAt(new Date());
                    sysUser.setUpdatedAt(new Date());

                    log.info("调用 addSysUser 创建用户");
                    // 使用 addSysUser 方法来自动设置默认头像
                    sysUserService.addSysUser(sysUser, CS.SYS_TYPE.AGENT);
                    log.info("用户创建成功，userId: {}", sysUser.getSysUserId());

                    // 更新密码（因为 addSysUser 使用默认密码，需要更新为指定密码）
                    if (!CS.DEFAULT_PWD.equals(loginPassword)) {
                        log.info("更新用户密码");
                        sysUserAuthService.resetAuthInfo(sysUser.getSysUserId(), null, null, loginPassword, CS.SYS_TYPE.AGENT);
                    }

                    // 更新代理商的初始用户ID和登录用户名
                    log.info("更新代理商初始用户ID");
                    agentInfo.setInitUserId(sysUser.getSysUserId());

                    // 尝试更新登录用户名，如果数据库字段不存在就忽略
                    try {
                        agentInfo.setLoginUsername(tempLoginUsername);
                        updateById(agentInfo);
                        log.info("代理商信息更新成功，包含loginUsername");
                    } catch (Exception e) {
                        log.warn("更新loginUsername字段失败，可能是数据库字段不存在，仅更新initUserId", e);
                        agentInfo.setLoginUsername(null);
                        updateById(agentInfo);
                        log.info("代理商信息更新成功，不包含loginUsername");
                    }
                    log.info("代理商用户创建完成");
                } else {
                    log.warn("loginUsername 为空，跳过用户创建");
                }
            } else {
                log.warn("loginPassword 为空，跳过用户创建");
            }
            
            return true;
        } catch (Exception e) {
            log.error("创建代理商和系统用户失败", e);
            throw e;
        }
    }

    /**
     * 分页查询代理商信息
     */
    public IPage<AgentInfo> selectPage(IPage<?> page, AgentInfo agentInfo, String currentAgentNo) {
        LambdaQueryWrapper<AgentInfo> wrapper = AgentInfo.gw();

        // 如果传入了当前代理商号，则查询其所有下级代理商（包括孙级代理商）
        if (StringUtils.isNotBlank(currentAgentNo)) {
            // 获取当前代理商信息
            AgentInfo currentAgent = getByAgentNo(currentAgentNo);
            if (currentAgent != null && StringUtils.isNotBlank(currentAgent.getAgentPath())) {
                // 基于代理商路径查询所有下级代理商
                wrapper.like(AgentInfo::getAgentPath, currentAgent.getAgentPath() + "/");
            } else {
                // 如果当前代理商不存在或路径为空，则查询直接下级代理商
                wrapper.eq(AgentInfo::getParentAgentNo, currentAgentNo);
            }
        }

        if (agentInfo != null) {
            // 代理商名称模糊查询
            if (StringUtils.isNotBlank(agentInfo.getAgentName())) {
                wrapper.like(AgentInfo::getAgentName, agentInfo.getAgentName());
            }
            // 代理商号精确查询
            if (StringUtils.isNotBlank(agentInfo.getAgentNo())) {
                wrapper.eq(AgentInfo::getAgentNo, agentInfo.getAgentNo());
            }
            // 状态查询
            if (agentInfo.getState() != null) {
                wrapper.eq(AgentInfo::getState, agentInfo.getState());
            }
            // 代理商层级查询
            if (agentInfo.getAgentLevel() != null) {
                wrapper.eq(AgentInfo::getAgentLevel, agentInfo.getAgentLevel());
            }
            // 联系电话查询
            if (StringUtils.isNotBlank(agentInfo.getContactTel())) {
                wrapper.like(AgentInfo::getContactTel, agentInfo.getContactTel());
            }
            // 如果查询条件中指定了上级代理商号，则覆盖currentAgentNo的过滤
            if (StringUtils.isNotBlank(agentInfo.getParentAgentNo())) {
                wrapper.eq(AgentInfo::getParentAgentNo, agentInfo.getParentAgentNo());
            }
        }

        wrapper.orderByDesc(AgentInfo::getCreatedAt);
        return page(Page.of(page.getCurrent(), page.getSize()), wrapper);
    }

    /**
     * 分页查询代理商信息（管理端使用，不过滤上级代理商）
     */
    public IPage<AgentInfo> selectPage(IPage<?> page, AgentInfo agentInfo) {
        return selectPage(page, agentInfo, null);
    }

    /**
     * 检查代理商号是否存在
     */
    public boolean isExistAgentNo(String agentNo) {
        return count(AgentInfo.gw().eq(AgentInfo::getAgentNo, agentNo)) > 0;
    }

    /**
     * 更新代理商信息
     */
    public boolean updateAgent(AgentInfo agentInfo) {
        agentInfo.setUpdatedAt(new Date());
        return updateById(agentInfo);
    }

    /**
     * 创建代理商（简单版本，用于管理端）
     */
    public boolean createAgent(AgentInfo agentInfo) {
        try {
            // 自动生成代理商号
            if (StringUtils.isBlank(agentInfo.getAgentNo())) {
                agentInfo.setAgentNo(SeqKit.genAgentNo());
            }

            // 构建代理商路径
            String agentPath = buildAgentPath(agentInfo.getParentAgentNo(), agentInfo.getAgentNo());
            agentInfo.setAgentPath(agentPath);
            
            // 设置代理商层级
            if (StringUtils.isBlank(agentInfo.getParentAgentNo())) {
                agentInfo.setAgentLevel((byte) 1);
            } else {
                AgentInfo parentAgent = getByAgentNo(agentInfo.getParentAgentNo());
                if (parentAgent != null) {
                    agentInfo.setAgentLevel((byte) (parentAgent.getAgentLevel() + 1));
                }
            }
            
            // 设置默认状态
            if (agentInfo.getState() == null) {
                agentInfo.setState(AgentInfo.STATE_NORMAL);
            }
            
            return save(agentInfo);
        } catch (Exception e) {
            log.error("创建代理商失败", e);
            return false;
        }
    }

    /**
     * 检查是否可以删除代理商
     */
    public boolean canDeleteAgent(String agentNo) {
        // 检查是否有下级代理商
        long subAgentCount = count(AgentInfo.gw().eq(AgentInfo::getParentAgentNo, agentNo));
        return subAgentCount == 0;
    }

    /**
     * 删除代理商及其相关用户数据
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAgentWithUser(String agentNo) {
        try {
            // 1. 检查是否可以删除
            if (!canDeleteAgent(agentNo)) {
                log.warn("代理商{}下存在下级代理商，无法删除", agentNo);
                return false;
            }

            // 2. 查找该代理商关联的用户
            List<SysUser> agentUsers = sysUserService.list(
                SysUser.gw()
                    .eq(SysUser::getSysType, CS.SYS_TYPE.AGENT)
                    .eq(SysUser::getBelongInfoId, agentNo)
            );

            // 3. 删除用户相关数据
            for (SysUser user : agentUsers) {
                Long userId = user.getSysUserId();

                // 删除用户认证信息
                sysUserAuthService.remove(
                    SysUserAuth.gw().eq(SysUserAuth::getUserId, userId)
                );

                // 删除用户角色关联
                sysUserRoleRelaService.remove(
                    SysUserRoleRela.gw().eq(SysUserRoleRela::getUserId, userId)
                );

                // 删除系统用户
                sysUserService.removeById(userId);

                log.info("删除代理商{}关联的用户: {}", agentNo, user.getLoginUsername());
            }

            // 4. 删除代理商信息
            boolean result = removeById(agentNo);
            if (result) {
                log.info("成功删除代理商: {}", agentNo);
            }

            return result;
        } catch (Exception e) {
            log.error("删除代理商{}失败", agentNo, e);
            throw new RuntimeException("删除代理商失败: " + e.getMessage());
        }
    }

    /**
     * 构建代理商路径
     */
    private String buildAgentPath(String parentAgentNo, String agentNo) {
        if (StringUtils.isBlank(parentAgentNo)) {
            return "/" + agentNo;
        }

        AgentInfo parentAgent = getByAgentNo(parentAgentNo);
        if (parentAgent == null) {
            throw new BizException("上级代理商不存在");
        }

        return parentAgent.getAgentPath() + "/" + agentNo;
    }

    /**
     * 获取指定代理商的所有下级代理商号列表（包括自己）
     * @param agentNo 代理商号
     * @return 代理商号列表（包括自己和所有下级代理商）
     */
    public List<String> getAllSubAgentNos(String agentNo) {
        List<String> agentNos = new ArrayList<>();

        // 首先添加自己
        agentNos.add(agentNo);

        // 获取当前代理商信息
        AgentInfo currentAgent = getByAgentNo(agentNo);
        if (currentAgent == null) {
            return agentNos;
        }

        // 根据代理商路径查询所有下级代理商
        List<AgentInfo> subAgents = baseMapper.selectSubAgentsByPath(currentAgent.getAgentPath());
        if (subAgents != null && !subAgents.isEmpty()) {
            for (AgentInfo subAgent : subAgents) {
                agentNos.add(subAgent.getAgentNo());
            }
        }

        return agentNos;
    }

    /**
     * 统计指定代理商的所有下级代理商数量（不包括自己）
     * @param agentNo 代理商号
     * @return 下级代理商数量
     */
    public long countAllSubAgents(String agentNo) {
        // 获取当前代理商信息
        AgentInfo currentAgent = getByAgentNo(agentNo);
        if (currentAgent == null || StringUtils.isBlank(currentAgent.getAgentPath())) {
            return 0;
        }

        // 基于代理商路径统计所有下级代理商
        return count(AgentInfo.gw()
                .like(AgentInfo::getAgentPath, currentAgent.getAgentPath() + "/")
                .eq(AgentInfo::getState, AgentInfo.STATE_NORMAL));
    }
}